
export function Footer() {
  return (
    <>
      {/* Footer for larger screens only */}
      <div className="hidden lg:block fixed bottom-8 left-8 text-left z-[999999] lg:w-2/4 lg:max-w-[34%]">
        <div className="flex flex-row items-center justify-between my-2">
          <div className="text-[#7e7b76] text-xs font-manrope_1 mr-[7rem]">
 <p className="text-sm text-[#7e7b76] font-manrope_1">
                        By signing in, you agree to our{" "}
                        <a href="/terms" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                          Terms
                        </a>{" "}
                        and{" "}
                        <a href="/privacy" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                          Privacy Policy
                        </a>
                      </p>          
        
          </div>
        </div>
      </div>

      {/* Footer for mobile only */}
      <div className="lg:hidden w-full max-w-[1600px] mx-auto px-4 md:px-8 py-8 mt-8">
        <div className="flex flex-col items-start justify-start text-left w-full">
          <div className="flex w-full flex-row items-center justify-between">
 <p className="text-sm text-[#7e7b76] font-manrope_1">
                        By signing in, you agree to our{" "}
                        <a href="/terms" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                          Terms
                        </a>{" "}
                        and{" "}
                        <a href="/privacy" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                          Privacy Policy
                        </a>
                      </p>            
                      
          </div>
        </div>
      </div>
    </>
  );
}
