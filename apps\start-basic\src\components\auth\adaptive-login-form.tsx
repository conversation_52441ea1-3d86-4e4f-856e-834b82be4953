// adaptive-login-form.tsx
import { useState, useEffect } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import { motion, AnimatePresence } from "framer-motion";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, ChevronDown, ChevronUp } from "lucide-react";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

interface OAuthProvider {
  id: 'google' | 'github' | 'apple';
  name: string;
  component: React.ReactNode;
  available: boolean;
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [currentImageLoaded, setCurrentImageLoaded] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const isFromMobile = from === 'mobile';

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
    setIsDropdownOpen(false);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Define available OAuth providers
  const availableProviders: OAuthProvider[] = [
    {
      id: 'google',
      name: 'Google',
      component: <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'google' || authProvider === 'all'
    },
    {
      id: 'github',
      name: 'GitHub',
      component: <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile // Only show GitHub for direct web access
    },
    {
      id: 'apple',
      name: 'Apple',
      component: (
        <div className="text-center">
          <p className="text-sm text-[#7e7b76] mb-1 font-manrope_1">Apple Sign In</p>
          <p className="text-xs text-[#7e7b76] opacity-70 font-manrope_1">
            Only available on iOS devices
          </p>
        </div>
      ),
      available: isFromMobile && (authProvider === 'apple' || authProvider === 'all')
    }
  ].filter(provider => provider.available);

  // Determine primary and secondary providers
  const getPrimaryProvider = (): OAuthProvider => {
    if (isFromMobile && authProvider) {
      const preferredProvider = availableProviders.find(p => p.id === authProvider);
      if (preferredProvider) return preferredProvider;
    }
    // Default to Google if available, otherwise first available
    return availableProviders.find(p => p.id === 'google') || availableProviders[0];
  };

  const primaryProvider = getPrimaryProvider();
  const secondaryProviders = availableProviders.filter(p => p.id !== primaryProvider?.id);

  // Track when the image is loaded
  useEffect(() => {
    setCurrentImageLoaded(false);
    const img = new Image();
    img.src = "/MiloSignin.png";
    img.onload = () => setCurrentImageLoaded(true);
    img.onerror = () => {
      console.warn("Failed to load MiloSignin.png image");
      setCurrentImageLoaded(true);
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('oauth-dropdown');
      if (dropdown && !dropdown.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  return (
    <div className="w-full min-h-screen relative bg-[#e9e5dc] dark:bg-[#1e1b16]">
      {/* Navigation bar - matching account pages */}
      {isFromMobile && (
        <div className="fixed h-20 top-0 md:top-7 right-0 left-0 md:left-3 md:right-4 w-full md:w-[38%] backdrop-blur-sm justify-between px-4 z-[100005] items-center gap-2 py-1">
          <div className="flex items-center justify-between">
            <div className="font-manrope_1 flex gap-4 items-center">
              <span className="text-black dark:text-white text-sm md:text-base font-manrope_1 font-bold">Mobile Login</span>
            </div>
            <button
              type="button"
              onClick={handleBackToMobile}
              className="flex items-center text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm md:text-base font-manrope_1"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Mobile App
            </button>
          </div>
        </div>
      )}

      <div className="w-full h-full max-w-[1600px] mx-auto px-4 md:px-8 py-12 lg:py-8 flex flex-col lg:flex-row items-start gap-12 lg:gap-8 relative z-10">
        {/* Spacer div for fixed sidebar on larger screens */}
        <div className="hidden lg:block lg:w-2/4 lg:max-w-[30%]"></div>

        {/* Sidebar - fixed on larger screens */}
        <div className="w-full lg:w-2/4 text-left mt-4 lg:mt-[10rem] lg:fixed lg:max-w-[34%]">
          <div className="story-margin" id="login-content">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-black dark:from-white via-gray-700 dark:via-gray-300 to-gray-600 dark:to-gray-400 py-4 font-manrope_1">
              {isFromMobile ? (
                <>Welcome <span className="italic">back</span></>
              ) : (
                <>Sign <span className="italic">in</span></>
              )}
            </h1>
            <p className="text-sm md:text-lg text-[#7e7b76] font-manrope_1 max-w-full mx-auto lg:mx-0">
              {isFromMobile
                ? "Continue your fitness journey from where you left off in the mobile app"
                : "Secure access for existing members to the admin dashboard and premium features"
              }
            </p>
          </div>
        </div>

        {/* Main content */}
        <div className="w-full lg:w-3/4 relative lg:pr-0 lg:flex lg:flex-col lg:items-end lg:z-30">
          {/* Login content in card format */}
          <div className="lg:w-[54vw] lg:float-right lg:mr-0 relative lg:max-h-[calc(100vh-0px)] rounded-xl shadow-md dark:shadow-white/10">

            {/* Login content wrapper */}
            <div className="w-full overflow-hidden rounded-xl">
              {/* Header section - matching account pages */}
              <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
                <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
                  {isFromMobile
                    ? "Continue your journey from the mobile app..."
                    : "Secure access for existing members only..."
                  }
                </div>
              </div>

              {/* Main login content - matching account pages */}
              <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
                <div className="p-6 md:p-8 space-y-6">

                  {/* Error message */}
                  {errorMessage && (
                    <div className="w-full p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                      <p className="text-red-700 dark:text-red-300 text-sm font-manrope_1">{errorMessage}</p>
                    </div>
                  )}

                  {/* Welcome Image Card - matching account page card style */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f] overflow-hidden">
                    <div className="w-full flex items-center justify-center relative">
                      <div
                        className={`absolute inset-0 bg-[#1e1b16] z-10 transition-opacity duration-300 ${
                          currentImageLoaded ? "opacity-0" : "opacity-100"
                        }`}
                      />
                      <img
                        src="/MiloSignin.png"
                        alt="Milo Sign In"
                        className="w-full h-auto max-h-[300px] sm:max-h-[350px] md:max-h-[400px] lg:max-h-[calc(100vh-356px)] opacity-80 relative z-[2]"
                        style={{ opacity: currentImageLoaded ? 0.8 : 0 }}
                        onLoad={() => setCurrentImageLoaded(true)}
                      />
                    </div>
                  </Card>

                  {/* Authentication Card - matching account page card style */}
                  <Card className="bg-[#e9e5dc] dark:bg-[#1e1b16] border-[#d6d1c4] dark:border-[#29261f]">
                    <CardHeader>
                      <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                        {isFromMobile ? "Continue from Mobile" : "Choose Sign-in Method"}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Context-specific messaging */}
                      <div className="space-y-4">
                        {isFromMobile ? (
                          <>
                            {authProvider === 'apple' && (
                              <p className="text-[#7e7b76] text-sm font-manrope_1">
                                Sign in with the same Apple account you used in the mobile app
                              </p>
                            )}
                            {authProvider === 'google' && (
                              <p className="text-[#7e7b76] text-sm font-manrope_1">
                                Sign in with the same Google account you used in the mobile app
                              </p>
                            )}
                            {authProvider === 'all' && (
                              <p className="text-[#7e7b76] text-sm font-manrope_1">
                                Sign in with any method since you have multiple authentication options set up
                              </p>
                            )}
                          </>
                        ) : (
                          <p className="text-[#7e7b76] font-manrope_1 text-sm">
                            Select your preferred authentication method to access the admin dashboard
                          </p>
                        )}
                      </div>

                    {/* OAuth Buttons Section */}
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">

                      {/* Primary OAuth Provider */}
                      <div className="flex-1 max-w-[200px] w-full">
                        {primaryProvider?.component}
                      </div>

                      {/* Dropdown for additional providers */}
                      {secondaryProviders.length > 0 && (
                        <div className="relative" id="oauth-dropdown">
                          <button
                            type="button"
                            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                            className="p-3 text-[#7e7b76] hover:text-black dark:hover:text-white transition-colors rounded-md hover:bg-[#f5f2ea] dark:hover:bg-[#0f0c05] border border-[#d6d1c4] dark:border-[#29261f]"
                            aria-label="More sign-in options"
                          >
                            <motion.div
                              animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronDown className="w-4 h-4" />
                            </motion.div>
                          </button>

                          {/* Dropdown Menu */}
                          <AnimatePresence>
                            {isDropdownOpen && (
                              <motion.div
                                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                                animate={{ opacity: 1, y: 0, scale: 1 }}
                                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                                transition={{ duration: 0.2 }}
                                className="absolute bottom-full mb-2 right-0 bg-white dark:bg-[#1e1b16] border border-[#d6d1c4] dark:border-[#29261f] rounded-lg shadow-lg overflow-hidden z-50 min-w-[250px]"
                              >
                                <div className="p-2">
                                  <div className="text-xs font-manrope_1 text-[#7e7b76] px-3 py-2 border-b border-[#d6d1c4] dark:border-[#29261f]">
                                    More sign-in options
                                  </div>
                                  {secondaryProviders.map((provider, index) => (
                                    <motion.div
                                      key={provider.id}
                                      initial={{ opacity: 0, x: -10 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ delay: index * 0.1 }}
                                      className="p-3 hover:bg-[#f5f2ea] dark:hover:bg-[#0f0c05] transition-colors"
                                      onClick={() => setIsDropdownOpen(false)}
                                    >
                                      {provider.component}
                                    </motion.div>
                                  ))}
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </div>
                      )}
                    </div>
                    </CardContent>
                  </Card>

                  {/* Terms and Privacy */}
                  <div className="text-center pt-4 border-t border-[#d6d1c4] dark:border-[#29261f]">
                    <p className="text-xs text-[#7e7b76] font-manrope_1">
                      By signing in, you agree to our{" "}
                      <a href="/terms" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                        Terms
                      </a>{" "}
                      and{" "}
                      <a href="/privacy" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                        Privacy Policy
                      </a>
                    </p>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}